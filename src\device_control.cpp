#include "device_control.h"
#include <OneButton.h>
#include <FastLED.h>
#include "tuya_log.h"
#include "shipping.h"
#include "esp32_adc.h"
#include "tuya_config.h"

// 引脚定义
#define LED_PIN 10
#define BTN_PIN_6 6
#define BTN_PIN_7 7
#define GPIO_PIN_5 5
#define GPIO_PIN_1 1
#define GPIO_PIN_0 0

int count_down_time = 60;
unsigned long pump_ozone_start_time = 0;

adc_config_t adc_config_pump = {
    .voltage_divider_factor = 1.00,
    .adc_pin = 3,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC1_CHANNEL_3,
    .adc_atten = ADC_ATTEN_DB_12,
    .adc_width = ADC_WIDTH_BIT_12,
    .default_vref = 1100,
    .adc1_chan_mask = BIT(3),
    .adc2_chan_mask = 0x0,
    .channel = {(adc_channel_t)ADC1_CHANNEL_3},
};

adc_config_t adc_config_vbat = {
    .voltage_divider_factor = 2.00,
    .adc_pin = 4,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC1_CHANNEL_4,
    .adc_atten = ADC_ATTEN_DB_12,
    .adc_width = ADC_WIDTH_BIT_12,
    .default_vref = 1100,
    .adc1_chan_mask = BIT(4),
    .adc2_chan_mask = 0x0,
    .channel = {(adc_channel_t)ADC1_CHANNEL_4},
};

// LED 配置
#define NUM_LEDS 1
CRGB leds[NUM_LEDS];

// OneButton 实例 (低电平有效, 启用内部上拉)
// Declared static to avoid conflicts with other OneButton instances in the project (e.g., in shipping.cpp)
static OneButton button6(BTN_PIN_6, true, true);
static OneButton button7(BTN_PIN_7, true, true);

// 状态变量
enum LedMode {
    MODE_WIFI_CONNECT_STATUS,
    MODE_TUYAIOT_STATUS,
    MODE_PUMP_FLASH,
    MODE_OZONE_FLASH,
    MODE_FACTORY_RESET_BLINK
};

volatile LedMode current_led_mode = MODE_TUYAIOT_STATUS;
volatile bool tuyaiot_connected = false;
volatile bool wifi_connected = false;
bool gpios_on = false; // 新增一个独立的GPIO状态变量
int pump_count = 0; // 电机空转运行计数
bool pump_dry_run_flag = false; // 电机空转运行标志

// 恢复出厂设置相关变量
unsigned long factory_reset_start_time = 0; // 长按开始时间
bool factory_reset_in_progress = false; // 是否正在进行恢复出厂设置流程
volatile int factory_blink_delay = 500; // LED闪烁延迟时间（毫秒）
static bool reset_triggered = false; // 静态变量移到全局，便于重置

// 函数声明
void handle_button6_click();
void handle_button7_click();
void handle_long_press_start();
void handle_during_long_press();
void handle_long_press_stop();
void led_task(void *pvParameters);
void bind_button_events(); // 新增：绑定按键事件的统一函数
extern "C" void report_tuya(int dpid, int value, bool on);

extern "C" bool is_pump_zone_run() {
    return (current_led_mode == MODE_PUMP_FLASH || current_led_mode == MODE_OZONE_FLASH);
}

void device_control_init()
{
    // 初始化 ADC采样电机和电池
    adc_single_init(&adc_config_pump);
    adc_calibration(&adc_config_pump);
    adc_single_init(&adc_config_vbat);
    adc_calibration(&adc_config_vbat);
    // 初始化 GPIO
    pinMode(GPIO_PIN_5, OUTPUT);
    pinMode(GPIO_PIN_1, OUTPUT);
    pinMode(GPIO_PIN_0, OUTPUT);
    digitalWrite(GPIO_PIN_5, LOW);
    digitalWrite(GPIO_PIN_1, LOW);
    digitalWrite(GPIO_PIN_0, LOW);  //关断水泵和臭氧总电源

    // 检查按钮是否仍然被按下（可能是从shipping模式唤醒）
    bool button6_pressed = (digitalRead(BTN_PIN_6) == LOW);
    bool button7_pressed = (digitalRead(BTN_PIN_7) == LOW);

    if (button6_pressed || button7_pressed) {
        TY_LOGI("Button still pressed during init, waiting for release...");
        // 等待按钮释放，最多等待5秒
        unsigned long wait_start = millis();
        while ((digitalRead(BTN_PIN_6) == LOW || digitalRead(BTN_PIN_7) == LOW) &&
               (millis() - wait_start < 5000)) {
            vTaskDelay(pdMS_TO_TICKS(50));
        }

        // 检查是否因为超时退出循环
        if (digitalRead(BTN_PIN_6) == LOW || digitalRead(BTN_PIN_7) == LOW) {
            TY_LOGW("Button still pressed after 5s timeout! Delaying button initialization...");
            // 如果按钮仍然被按下，延迟初始化按钮事件
            // 先初始化按钮对象但不绑定事件处理器
            button6.setDebounceMs(50);
            button7.setDebounceMs(50);

            // 创建一个延迟任务来等待按钮释放后再绑定事件
            xTaskCreate([](void* param) {
                TY_LOGI("Delayed button initialization task started");
                // 继续等待按钮释放
                while (digitalRead(BTN_PIN_6) == LOW || digitalRead(BTN_PIN_7) == LOW) {
                    vTaskDelay(pdMS_TO_TICKS(100));
                }
                TY_LOGI("Button finally released, binding events now");

                // 现在安全地绑定按键事件
                bind_button_events();

                TY_LOGI("Button events bound successfully");
                vTaskDelete(NULL); // 删除自己
            }, "delayed_button_init", 2048, NULL, 1, NULL);

            TY_LOGI("Button initialization delayed due to pressed state");
            // 不要提前返回，继续执行后续的任务创建
        } else {
            TY_LOGI("Button released, continuing normal initialization");
            // 正常情况下立即绑定按键事件
            bind_button_events();
        }
    } else {
        // 按钮未被按下，正常绑定事件
        bind_button_events();
    }
}

void device_control_loop()
{
    button6.tick();
    button7.tick();
}

void device_control_tuya_iot_status(bool connected)
{
    tuyaiot_connected = connected;
    current_led_mode = MODE_TUYAIOT_STATUS;
}

void device_control_wifi_connected_flash(bool connected)
{
    current_led_mode = MODE_WIFI_CONNECT_STATUS;
    wifi_connected = connected;
}

void handle_button6_click() // PUMP
{
    TY_LOGI("Button 6 clicked");
    pump_count = 0;
    pump_dry_run_flag = false;
    // Case 1: GPIOs are on and we are showing BLUE. Turn everything off.
    if (gpios_on && current_led_mode == MODE_PUMP_FLASH) {
        gpios_on = false;
        current_led_mode = MODE_TUYAIOT_STATUS;
        digitalWrite(GPIO_PIN_5, LOW);
        digitalWrite(GPIO_PIN_1, LOW);
        digitalWrite(GPIO_PIN_0, LOW);  //水泵和臭氧总电源
        report_tuya(SWITCH_PUMP_ID, 0, false);
    }
    // Case 2: GPIOs are off, OR we are showing GREEN. Turn on GPIOs and switch to RED.
    else {
        gpios_on = true;
        current_led_mode = MODE_PUMP_FLASH;
        digitalWrite(GPIO_PIN_0, HIGH);  //水泵和臭氧总电源
        vTaskDelay(pdMS_TO_TICKS(50));
        digitalWrite(GPIO_PIN_5, HIGH);
        digitalWrite(GPIO_PIN_1, HIGH);
        pump_ozone_start_time = millis();
        report_tuya(SWITCH_PUMP_ID, 0, true);
        report_tuya(SWITCH_OZONE_ID, 0, false);
    }
}

void handle_button7_click() // OZONE
{
    TY_LOGI("Button 7 clicked");
    pump_count = 0;
    pump_dry_run_flag = false;
    // Case 1: GPIOs are on and we are showing GREEN. Turn everything off.
    if (gpios_on && current_led_mode == MODE_OZONE_FLASH) {
        gpios_on = false;
        current_led_mode = MODE_TUYAIOT_STATUS;
        digitalWrite(GPIO_PIN_5, LOW);
        digitalWrite(GPIO_PIN_1, LOW);
        digitalWrite(GPIO_PIN_0, LOW);  //水泵和臭氧总电源
        report_tuya(SWITCH_OZONE_ID, 0, false);
    }
    // Case 2: GPIOs are off, OR we are showing BLUE. Turn on GPIOs and switch to GREEN.
    else {
        gpios_on = true;
        current_led_mode = MODE_OZONE_FLASH;
        digitalWrite(GPIO_PIN_0, HIGH);  //水泵和臭氧总电源
        vTaskDelay(pdMS_TO_TICKS(50));
        digitalWrite(GPIO_PIN_5, HIGH);
        digitalWrite(GPIO_PIN_1, HIGH);
        pump_ozone_start_time = millis();
        report_tuya(SWITCH_OZONE_ID, 0, true);
        report_tuya(SWITCH_PUMP_ID, 0, false);
    }
}

void handle_long_press_start()
{
    TY_LOGI("Long press start detected (3s). Starting factory reset blink.");
    factory_reset_start_time = millis(); // 记录长按开始时间
    factory_reset_in_progress = true;
    current_led_mode = MODE_FACTORY_RESET_BLINK;
}

void handle_during_long_press()
{
    if (reset_triggered) return;

    // 获取当前长按时间（从按钮最初按下开始计算）
    unsigned long press_time = max(button6.getPressedMs(), button7.getPressedMs());

    TY_LOGI("Current press time: %lums", press_time);

    // 检查任一按钮的长按时间是否超过10秒（10000ms）
    if (press_time >= 10000) {
        reset_triggered = true;
        TY_LOGI("Factory reset triggered by holding for 10s! (actual: %lums)", press_time);
        factory_reset();
        ESP.restart();
    }

    // 动态调整LED闪烁速度（从3秒开始，到10秒时最快）
    if (press_time >= 3000 && factory_reset_in_progress) {
        // 计算从3秒开始的有效时间（最多7秒）
        unsigned long effective_time = press_time - 3000;
        if (effective_time > 7000) effective_time = 7000;

        // 计算闪烁延迟：从500ms线性减少到50ms
        int new_blink_delay = 500 - (effective_time * 450 / 7000);
        if (new_blink_delay < 50) new_blink_delay = 50;

        // 更新全局闪烁延迟变量（在led_task中使用）
        factory_blink_delay = new_blink_delay;

        TY_LOGD("Press time: %lums, effective: %lums, blink delay: %dms",
                press_time, effective_time, factory_blink_delay);
    }
}

void handle_long_press_stop()
{
    TY_LOGI("Long press stopped.");

    // 重置恢复出厂设置状态
    factory_reset_in_progress = false;
    factory_reset_start_time = 0;
    factory_blink_delay = 500; // 重置闪烁延迟

    // 重置handle_during_long_press中的静态变量
    reset_triggered = false;

    // 如果长按被释放（无论是否触发了重置），都恢复正常状态
    // 如果已经触发重置，设备会重启，这里只是一个安全回退
    current_led_mode = MODE_TUYAIOT_STATUS;
}

bool is_dry_run()
{   
    float voltage = ReadVoltageSingle(&adc_config_pump);
    TY_LOGD("Pump Voltage: %.2fV", voltage);
    return (voltage < 1.85f);
}

void pump_ozone_stop(int dpid)
{
    current_led_mode = MODE_TUYAIOT_STATUS;
    digitalWrite(GPIO_PIN_5, LOW);
    digitalWrite(GPIO_PIN_1, LOW);
    digitalWrite(GPIO_PIN_0, LOW); // 水泵和臭氧总电源
    report_tuya(dpid, 0, false);
}

void led_task(void *pvParameters)
{
    bool led_on = false;
    int factory_blink_delay = 500;
    int count = 0, debug_stack_count = 40;
    UBaseType_t stackLeft;
    uint8_t breathing_phase = 0; // 呼吸灯相位，0-255

    // 初始化 FastLED
    FastLED.addLeds<WS2812B, LED_PIN, GRB>(leds, NUM_LEDS);
    FastLED.setBrightness(255);

    while (true) {
        // 如果不是在恢复出厂设置模式，则重置闪烁延迟
        if (current_led_mode != MODE_FACTORY_RESET_BLINK) {
            factory_blink_delay = 500;
        }

        switch (current_led_mode) {
            case MODE_TUYAIOT_STATUS:
                if (tuyaiot_connected) {
                    // TuyaIoT 已连接：呼吸灯模式 (DarkViolet)
                    breathing_phase += 1; // 减慢呼吸速度，让效果更明显

                    // 使用正弦波计算亮度 (0-255)
                    // sin8() 返回 0-255，我们将其映射到更大的亮度范围以增强效果
                    uint8_t brightness = sin8(breathing_phase);
                    brightness = map(brightness, 0, 255, 0, 120);

                    leds[0] = CRGB::DarkViolet;
                    leds[0].nscale8(brightness);

                    FastLED.show();
                    if ( brightness != 0 ) {
                        debug_stack_count = 1000;
                        vTaskDelay(pdMS_TO_TICKS(10)); // 减少延迟以保持平滑度
                    }
                    else {
                        debug_stack_count = 40;
                        vTaskDelay(pdMS_TO_TICKS(250));
                    }
                } else {
                    // TuyaIoT 未连接：闪烁模式 (Red)
                    led_on = !led_on;
                    if (led_on) {
                        leds[0] = CRGB::Red;
                        leds[0].nscale8(80);
                    } else {
                        leds[0] = CRGB::Black;
                    }
                    FastLED.show();
                    vTaskDelay(pdMS_TO_TICKS(500)); // 闪烁间隔
                }
                break;

            case MODE_PUMP_FLASH:
                if (!pump_dry_run_flag) {
                    //led_on = !led_on;
                    //leds[0] = led_on ? CRGB::Green : CRGB::Black;
                    leds[0] = CRGB::Green;
                    leds[0].nscale8(80);                   
                } else {
                    led_on = !led_on;
                    leds[0] = led_on ? CRGB::Red : CRGB::Black;
                    leds[0].nscale8(80);
                }
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(250));

                pump_dry_run_flag = is_dry_run();
                if (!pump_dry_run_flag)
                    pump_count = 0;
                else
                    pump_count++;
                // 连续20次检测到空转或灌溉时长到达，则退出水泵模式
                if (pump_count >= 20 || (millis() - pump_ozone_start_time) / 1000 >= count_down_time)
                    pump_ozone_stop(SWITCH_PUMP_ID);
                break;

            case MODE_OZONE_FLASH:
                if (!pump_dry_run_flag) {
                    //led_on = !led_on;
                    //leds[0] = led_on ? CRGB::Blue : CRGB::Black;
                    leds[0] = CRGB::Blue;
                    leds[0].nscale8(200);                   
                } else {
                    led_on = !led_on;
                    leds[0] = led_on ? CRGB::Red : CRGB::Black;
                    leds[0].nscale8(80);
                }
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(250));

                pump_dry_run_flag = is_dry_run();
                if (!pump_dry_run_flag)
                    pump_count = 0;
                else
                    pump_count++;
                // 连续20次检测到空转或灌溉时长到达，则退出水泵模式
                if (pump_count >= 20 || (millis() - pump_ozone_start_time) / 1000 >= count_down_time)
                    pump_ozone_stop(SWITCH_OZONE_ID);
                break;
            
            case MODE_FACTORY_RESET_BLINK:
                // 执行红色闪烁（闪烁速度由handle_during_long_press动态控制）
                leds[0] = CRGB::Red;
                leds[0].nscale8(120); // 增加亮度让闪烁更明显
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(factory_blink_delay));
                leds[0] = CRGB::Black;
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(factory_blink_delay));
                break;

            case MODE_WIFI_CONNECT_STATUS:
                led_on = !led_on;
                if (led_on) {
                    leds[0] = wifi_connected ? CRGB::Yellow : CRGB::Red;
                    leds[0].nscale8(80);
                } else {
                    leds[0] = CRGB::Black;
                }
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(250));
                break;
        }
#if 0
        // 每约1-2秒打印一次采样结果
        if (++count >= 4) {
            float vref = ReadVoltageSingle(&adc_config_pump);
            TY_LOGI("[led_task] Pump Vref: %f", vref);
            count = 0;
        }
#else
        // 每约5秒打印一次堆栈剩余空间
        if (++count >= debug_stack_count) {
            stackLeft = uxTaskGetStackHighWaterMark(NULL);
            TY_LOGD("[led_task] Stack high water mark: %d", stackLeft);
            count = 0;
            debug_stack_count = 40;
        }
#endif
    }
}

/**
 * @brief 绑定按键事件的统一函数
 */
void bind_button_events() {
    TY_LOGI("Binding button events");

    // 绑定按钮6事件
    button6.attachClick(handle_button6_click);
    button6.attachLongPressStart(handle_long_press_start);
    button6.attachDuringLongPress(handle_during_long_press);
    button6.attachLongPressStop(handle_long_press_stop);
    button6.setPressMs(3000); // 长按3000ms后开始检测（开始闪烁）
    button6.setDebounceMs(50); // 增加去抖动以防止物理按键抖动导致误触发

    // 绑定按钮7事件
    button7.attachClick(handle_button7_click);
    button7.attachLongPressStart(handle_long_press_start);
    button7.attachDuringLongPress(handle_during_long_press);
    button7.attachLongPressStop(handle_long_press_stop);
    button7.setPressMs(3000); // 长按3000ms后开始检测（开始闪烁）
    button7.setDebounceMs(50); // 增加去抖动以防止物理按键抖动导致误触发
}
