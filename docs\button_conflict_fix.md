# 按钮冲突问题修复

## 问题描述

设备从shipping模式唤醒后，会意外触发水泵开关，导致用户长按唤醒设备的同时也开启了水泵。

## 问题根源

### 冲突的按钮用途
1. **shipping.cpp**: GPIO6/GPIO7用于长按3秒唤醒设备
2. **device_control.cpp**: GPIO6/GPIO7用于点击控制水泵开关

### 问题流程
1. 用户长按按钮3秒唤醒设备
2. 设备退出shipping模式，继续正常启动
3. `device_control_init()` 被调用，重新初始化按钮
4. **按钮仍然被按下**，触发新的按钮事件处理
5. 意外触发 `handle_button6_click()` 或 `handle_button7_click()`
6. 水泵被意外开启

## 解决方案

在 `device_control_init()` 中添加按钮释放检查，确保在绑定新的事件处理器之前，按钮已经被释放。

### 实现逻辑

```cpp
// 检查按钮是否仍然被按下（可能是从shipping模式唤醒）
bool button6_pressed = (digitalRead(BTN_PIN_6) == LOW);
bool button7_pressed = (digitalRead(BTN_PIN_7) == LOW);

if (button6_pressed || button7_pressed) {
    TY_LOGI("Button still pressed during init, waiting for release...");
    // 等待按钮释放，最多等待5秒
    unsigned long wait_start = millis();
    while ((digitalRead(BTN_PIN_6) == LOW || digitalRead(BTN_PIN_7) == LOW) && 
           (millis() - wait_start < 5000)) {
        vTaskDelay(pdMS_TO_TICKS(50));
    }
    TY_LOGI("Button released or timeout, continuing initialization");
}

// 然后才绑定按键事件
button6.attachClick(handle_button6_click);
// ...
```

## 工作流程

### 修复前的问题流程
1. 长按3秒 → 唤醒设备
2. 立即初始化按钮 → 按钮仍被按下
3. 触发点击事件 → 意外开启水泵

### 修复后的正常流程
1. 长按3秒 → 唤醒设备
2. 检查按钮状态 → 发现仍被按下
3. 等待按钮释放 → 最多等待5秒
4. 按钮释放后 → 绑定新的事件处理器
5. 正常运行 → 不会意外触发水泵

## 技术细节

### 检测方法
- 使用 `digitalRead()` 直接读取GPIO状态
- 低电平(LOW)表示按钮被按下
- 高电平(HIGH)表示按钮被释放

### 等待机制
- 最多等待5秒按钮释放
- 每50毫秒检查一次按钮状态
- 超时后继续初始化（防止死锁）

### 安全保障
- 超时保护：即使按钮一直被按下，5秒后也会继续
- 非阻塞：使用 `vTaskDelay()` 避免阻塞其他任务
- 日志记录：记录等待和释放状态

## 优势

1. **解决冲突**: 完全避免唤醒时意外触发水泵
2. **用户友好**: 用户可以在唤醒后立即释放按钮
3. **安全可靠**: 有超时保护，不会导致系统卡死
4. **简单有效**: 最小化的代码修改，最大化的效果

## 测试建议

1. **正常唤醒测试**:
   - 长按3秒唤醒设备
   - 立即释放按钮
   - 验证水泵不会被意外开启

2. **持续按压测试**:
   - 长按3秒唤醒设备
   - 继续按住按钮5秒以上
   - 验证系统不会卡死，最终正常初始化

3. **功能验证测试**:
   - 设备正常运行后
   - 点击按钮控制水泵
   - 验证水泵控制功能正常

## 注意事项

1. **等待时间**: 5秒的等待时间对用户来说是可接受的
2. **超时处理**: 超时后仍会继续初始化，确保系统稳定性
3. **兼容性**: 不影响正常的按钮功能和shipping模式唤醒
4. **性能影响**: 只在设备启动时执行一次，对性能影响微乎其微
